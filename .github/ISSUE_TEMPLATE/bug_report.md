---
name: Bug Report 🐞
about: Something isn't working as expected? Here is the right place to report.
labels: "bug"
---

<!--
  Please fill out each section below, otherwise, your issue will be closed. This info allows garak maintainers to diagnose (and fix!) your issue as quickly as possible.

  Useful Links:
  - Wiki: https://docs.garak.ai/garak

  Before opening a new issue, please search existing issues https://github.com/NVIDIA/garak/issues
-->

## Steps to reproduce

How'd you do it?

1. ...
2. ...

This section should also tell us any relevant information about the
environment; for example, if a probe that used to work is failing,
tell us the target application and possibly model versions.

## Were you following a specific guide/tutorial or reading documentation?

If yes link the guide/tutorial or documentation you were following here, otherwise you may omit this section.

## Expected behavior

What should happen?

## Current behavior

What happens instead?

### garak version

Get this with the `--version` flag on the command line (or `git log -1 --pretty=oneline` for a source install).

## Additional Information

1. Operating system
2. Python version
3. Install method (`pypi`, `pip` based repo install, direct repository checkout with `git`)
4. Logs from execution run `report.html` / `report.jsonl` / `hitlog.jsonl` and if possible `garak.log`
5. Details of execution config such as command line flags or config files
6. Any relevant hardware or resource information