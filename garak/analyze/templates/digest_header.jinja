<!DOCTYPE html>
<html lang="en">

<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="UTF-8" />
<style>
body {font-family: sans-serif}
:root{
  --defcon1: #f94144;
  --defcon2: #f8961e;
  --defcon3: #cccccc;
  --defcon4: #eeeeee;
  --defcon5: #f7f7ff;
}
.defcon1 {background-color: var(--defcon1); text-color: #000}
.defcon2 {background-color: var(--defcon2); text-color: #000}
.defcon3 {background-color: var(--defcon3); text-color: #000}
.defcon4 {background-color: var(--defcon4); text-color: #000}
.defcon5 {background-color: var(--defcon5); text-color: #000}
.probe {padding-left: 40pt}
.detector {padding-left: 65pt}
.score {
  padding-top: 6pt; 
  padding-bottom: 6pt; 
  /* margin-left: 60pt; */
  border: 1pt solid #ccc;
  margin-top: 4pt;
  margin-bottom: 4pt;
}
div.score p span {
  display: inline-block;
  width: 100pt
  }
.score b {
  padding: 6pt 10pt 7pt 10pt; 
  margin: 0
}
h2 {padding-left: 20pt}
h3 {padding-left: 40pt}
h4 {padding-left: 60pt}
h2,h3,h4 {
  padding-top: 10px;
  padding-bottom: 10px;
  border: 1px solid transparent;
  transition: 0.3s;
}
h3:hover, h4:hover {
  border: 1px solid #a0a0a0;
}
p.left {display: inline-block; margin-top:0; margin-bottom: 0}
span.dc {
  border: 1px solid #000; 
  font-size: 10pt; 
  font-weight: bold; 
  float: right;
  width: 28pt; 
  height: 12pt; 
  text-align: center; 
  margin-right: 15pt;
  }
/* Style the buttons that are used to open and close the accordion panel */
.accordion {
//  background-color: #eee;
  color: #444;
  cursor: pointer;
  padding: 18px;
  width: 100%;
  text-align: left;
  border: none;
  outline: none;
  transition: 0.4s;
  margin: 1pt;
}

/* Add a background color to the button if it is clicked on (add the .active class with JS), and when you move the mouse over it (hover) */
.accordion.active, .accordion:hover {
  background-color: #ccf;
}

/* Style the accordion panel. Note: hidden by default */
.panel {
  padding: 0 18px;
  background-color: white;
  display: none;
  overflow: hidden;
}
</style>
<title>garak report: {{reportfile}}</title>
<meta name="description" content="" />
</head>

<body>

<h1>garak run: {{reportfile}}</h1>
<button class="accordion">⚙️ view config</button>
<div style="border:solid black 1px; padding: 5px; margin: 5px" class="panel">
<h2>config details</h2>
<pre>
filename: <b>{{reportfile}}</b>

garak version: <b>{{garak_version}}</b>

target generator: <b>{{model_type}}.{{model_name}}</b>

run started at: <b>{{start_time}}</b>

run data digest generated at: <b>{{report_digest_time}}</b>

html report generated at: <b>{{now}}</b>

probe spec: <b>{{probespec}}</b>

run config: {{setup}}

group scoring function: <b>{{group_aggregation_function}}</b>

{% for payload in payloads %}
payload load: {{payload}}

{% endfor %}
</pre>
</div>

{%if model_name %}
<h2>Results: {{model_type}} / {{model_name}}</h2>
{%else%}
<h2>Results:</h2>
{%endif%}