# SPDX-FileCopyrightText: Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
#
#
# run all active probes, just once each, for a rapid broad test
---
system:
  lite: false

run:
  generations: 1

plugins:
  extended_detectors: true
  probe_spec: all
  buff_spec: paraphrase.Fast
  probes:
    encoding:
      payloads:
        - default,xss,slur_terms  


