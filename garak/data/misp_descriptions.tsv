owasp:llm01	LLM01: Prompt Injection	Crafty inputs can manipulate a Large Language Model, causing unintended actions. Direct injections overwrite system prompts, while indirect ones manipulate inputs from external sources.
owasp:llm02	LLM02: Insecure Output Handling	This vulnerability occurs when an LLM output is accepted without scrutiny, exposing backend systems. Misuse may lead to severe consequences like XSS, CSRF, SSRF, privilege escalation, or remote code execution.
owasp:llm03	LLM03: Training Data Poisoning	This occurs when LLM training data is tampered, introducing vulnerabilities or biases that compromise security, effectiveness, or ethical behavior. Sources include Common Crawl, WebText, OpenWebText, & books.
owasp:llm04	LLM04: Model Denial of Service	Attackers cause resource-heavy operations on Large Language Models leading to service degradation or high costs. The vulnerability is magnified due to the resource-intensive nature of LLMs and unpredictability of user inputs.
owasp:llm05	LLM05: Supply Chain Vulnerabilities	LLM application lifecycle can be compromised by vulnerable components or services, leading to security attacks. Using third-party datasets, pre- trained models, and plugins can add vulnerabilities.
owasp:llm06	LLM06: Sensitive Information Disclosure	LLMs may reveal confidential data in its responses, leading to unauthorized data access, privacy violations, and security breaches. It’s crucial to implement data sanitization and strict user policies to mitigate this.
owasp:llm07	LLM07: Insecure Plugin Design	LLM plugins can have insecure inputs and insufficient access control. This lack of application control makes them easier to exploit and can result in consequences like remote code execution.
owasp:llm08	LLM08: Excessive Agency	LLM-based systems may undertake actions leading to unintended consequences. The issue arises from excessive functionality, permissions, or autonomy granted to the LLM-based systems.
owasp:llm09	LLM09: Overreliance	Systems or people overly depending on LLMs without oversight may face misinformation, miscommunication, legal issues, and security vulnerabilities due to incorrect or inappropriate content generated by LLMs.
owasp:llm10	LLM10: Model Theft	This involves unauthorized access, copying, or exfiltration of proprietary LLM models. The impact includes economic losses, compromised competitive advantage, and potential access to sensitive information.
avid-effect:security:S0100	Software Vulnerability	Vulnerability in system around model—a traditional vulnerability
avid-effect:security:S0200	Supply Chain Compromise	Compromising development components of a ML model, e.g. data, model, hardware, and software stack.
avid-effect:security:S0201	Model Compromise	Infected model file
avid-effect:security:S0202	Software compromise	Upstream Dependency Compromise
avid-effect:security:S0300	Over-permissive API	Unintended information leakage through API
avid-effect:security:S0301	Information Leak	Cloud Model API leaks more information than it needs to
avid-effect:security:S0302	Excessive Queries	Cloud Model API isn’t sufficiently rate limited
avid-effect:security:S0400	Model Bypass	Intentionally try to make a model perform poorly
avid-effect:security:S0401	Bad Features	The model uses features that are easily gamed by the attacker
avid-effect:security:S0402	Insufficient Training Data	The bypass is not represented in the training data
avid-effect:security:S0403	Adversarial Example	Input data points intentionally supplied to draw mispredictions. Potential Cause: Over permissive API
avid-effect:security:S0500	Exfiltration	Directly or indirectly exfiltrate ML artifacts
avid-effect:security:S0501	Model inversion	Reconstruct training data through strategic queries
avid-effect:security:S0502	Model theft	Extract model functionality through strategic queries
avid-effect:security:S0600	Data poisoning	Usage of poisoned data in the ML pipeline
avid-effect:security:S0601	Ingest Poisoning	Attackers inject poisoned data into the ingest pipeline
avid-effect:ethics:E0100	Bias/Discrimination	Concerns of algorithms propagating societal bias
avid-effect:ethics:E0101	Group fairness	Fairness towards specific groups of people
avid-effect:ethics:E0102	Individual fairness	Fairness in treating similar individuals
avid-effect:ethics:E0200	Explainability	Ability to explain decisions made by AI
avid-effect:ethics:E0201	Global explanations	Explain overall functionality
avid-effect:ethics:E0202	Local explanations	Explain specific decisions
avid-effect:ethics:E0300	User actions	Perpetuating/causing/being affected by negative user actions
avid-effect:ethics:E0301	Toxicity	Users hostile towards other users
avid-effect:ethics:E0302	Polarization/ Exclusion	User behavior skewed in a significant direction
avid-effect:ethics:E0400	Misinformation	Perpetuating/causing the spread of falsehoods
avid-effect:ethics:E0401	Deliberative Misinformation	Generated by individuals., e.g. vaccine disinformation
avid-effect:ethics:E0402	Generative Misinformation	Generated algorithmically, e.g. Deep Fakes
avid-effect:performance:P0100	Data issues	Problems arising due to faults in the data pipeline
avid-effect:performance:P0101	Data drift	Input feature distribution has drifted
avid-effect:performance:P0102	Concept drift	Output feature/label distribution has drifted
avid-effect:performance:P0103	Data entanglement	Cases of spurious correlation and proxy features
avid-effect:performance:P0104	Data quality issues	Missing or low-quality features in data
avid-effect:performance:P0105	Feedback loops	Unaccounted for effects of an AI affecting future data collection
avid-effect:performance:P0200	Model issues	Ability for the AI to perform as intended
avid-effect:performance:P0201	Resilience/stability	Ability for outputs to not be affected by small change in inputs
avid-effect:performance:P0202	OOD generalization	Test performance doesn’t deteriorate on unseen data in training
avid-effect:performance:P0203	Scaling	Training and inference can scale to high data volumes
avid-effect:performance:P0204	Accuracy	Model performance accurately reflects realistic expectations
avid-effect:performance:P0300	Privacy	Protect leakage of user information as required by rules and regulations
avid-effect:performance:P0301	Anonymization	Protects through anonymizing user identity
avid-effect:performance:P0302	Randomization	Protects by injecting noise in data, eg. differential privacy
avid-effect:performance:P0303	Encryption	Protects through encrypting data accessed
avid-effect:performance:P0400	Safety	Minimizing maximum downstream harms
avid-effect:performance:P0401	Psychological Safety	Safety from unwanted digital content, e.g. NSFW
avid-effect:performance:P0402	Physical safety	Safety from physical actions driven by a AI system
avid-effect:performance:P0403	Socioeconomic safety	Safety from socioeconomic harms, e.g. harms to job prospects or social status
avid-effect:performance:P0404	Environmental safety	Safety from environmental harms driven by AI systems
quality:Robustness:GenerativeMisinformation	Generative Misinformation	Hallucinations and off-topic digressions (or other common misbehaviours of LLMs)
quality:Behavioral:ContentSafety:HateHarassment	Hate and harassment	Hate and harassment
quality:Behavioral:ContentSafety:LegalGoodsServices	Legal goods/services	Avoid illegal goods/services, and follow regulations for regulated goods/services
quality:Behavioral:ContentSafety:Profanity	Profanity	Profanity
quality:Behavioral:ContentSafety:SelfHarm	SelfHarm	Self-harm including cutting, suicide, anorexia/bulemia
quality:Behavioral:ContentSafety:Sexual	Sexual	Sexual
quality:Behavioral:ContentSafety:SexualMinor	Sexual (minor)	Sexual content in the context of a minor
quality:Behavioral:ContentSafety:Toxicity	Toxicity	Toxicity
quality:Behavioral:ContentSafety:Unauthorized	Unauthorized advice	Unauthorized advice on legal, financial, medical and personal fields
quality:Behavioral:ContentSafety:Violence	Violence	Public safety; harm to other people, animals, property;  threatened or actual; includes physical, mental, and sexual
quality:Security:Confidentiality	Confidentiality	Confidentiality
quality:Security:PromptStability	Prompt Stability	Prompt templating is robust to user input variation and provide stable outputs consistent with designer intents
quality:Security:ExtractionInversion	Extraction/Inversion	Leaking information about model data and construction
quality:Security:Integrity	Integrity	Detection of tainted training data etc.
quality:Security:Availability	Availability	Availability (Model DoS)
quality:Security:Adversarial	Adversarial Robustness	Adversarial Robustness
quality:Behavioral:DeliberativeMisinformation	Deliberative Misinformation​	Content that perpetuates or accelerates disinformation activities
risk-cards:lmrc:aggression_user	Aggression User	Aggression User
risk-cards:lmrc:anthropomorphisation	Anthropomorphisation	Anthropomorphisation
risk-cards:lmrc:astroturfing	Astroturfing	Astroturfing
risk-cards:lmrc:bad_medical_advice	Bad Medical Advice	Bad Medical Advice
risk-cards:lmrc:bad_social_advice	Bad Social Advice	Bad Social Advice
risk-cards:lmrc:bullying	Bullying	Bullying
risk-cards:lmrc:calls_for_conversion	Calls For Conversion	Calls For Conversion
risk-cards:lmrc:challenge_presuppositions	Challenge Presuppositions	Challenge Presuppositions
risk-cards:lmrc:conspiracy_theories	Conspiracy Theories	Conspiracy Theories
risk-cards:lmrc:csam	Csam	Csam
risk-cards:lmrc:cyber_weapon_instructions	Cyber Weapon Instructions	Cyber Weapon Instructions
risk-cards:lmrc:deadnaming	Deadnaming	Deadnaming
risk-cards:lmrc:deceptive_anthropomorphism	Deceptive Anthropomorphism	Deceptive Anthropomorphism
risk-cards:lmrc:demands_for_personal_information	Demands For Personal Information	Demands For Personal Information
risk-cards:lmrc:discrediting	Discrediting	Discrediting
risk-cards:lmrc:dos	Dos	Dos
risk-cards:lmrc:downplaying_credible_threats	Downplaying Credible Threats	Downplaying Credible Threats
risk-cards:lmrc:doxxing	Doxxing	Doxxing
risk-cards:lmrc:eating_disorder_promotion	Eating Disorder Promotion	Eating Disorder Promotion
risk-cards:lmrc:essentialising	Essentialising	Essentialising
risk-cards:lmrc:expressions_of_dominance	Expressions Of Dominance	Expressions Of Dominance
risk-cards:lmrc:fear_of_violence	Fear Of Violence	Fear Of Violence
risk-cards:lmrc:grooming_minors	Grooming Minors	Grooming Minors
risk-cards:lmrc:harmful_instructions	Harmful Instructions	Harmful Instructions
risk-cards:lmrc:hate_speech	Hate Speech	Hate Speech
risk-cards:lmrc:hegemonising_worldview	Hegemonising Worldview	Hegemonising Worldview
risk-cards:lmrc:holocaust_denial	Holocaust Denial	Holocaust Denial
risk-cards:lmrc:identify_attacks	Identify Attacks	Identify Attacks
risk-cards:lmrc:identity_misrepresentation	Identity Misrepresentation	Identity Misrepresentation
risk-cards:lmrc:idolisation	Idolisation	Idolisation
risk-cards:lmrc:impersonation	Impersonation	Impersonation
risk-cards:lmrc:inciting_animal_abuse	Inciting Animal Abuse	Inciting Animal Abuse
risk-cards:lmrc:inciting_self-harm	Inciting Self-Harm	Inciting Self-Harm
risk-cards:lmrc:inciting_violence	Inciting Violence	Inciting Violence
risk-cards:lmrc:manipulation	Manipulation	Manipulation
risk-cards:lmrc:misquoting	Misquoting	Misquoting
risk-cards:lmrc:mocking_people	Mocking People	Mocking People
risk-cards:lmrc:neosexism	Neosexism	Neosexism
risk-cards:lmrc:news_encyclopedia_hallucination	News Encyclopedia Hallucination	News Encyclopedia Hallucination
risk-cards:lmrc:nonrepresentativity	Nonrepresentativity	Nonrepresentativity
risk-cards:lmrc:objectification	Objectification	Objectification
risk-cards:lmrc:outdated_false_info	Outdated False Info	Outdated False Info
risk-cards:lmrc:person_group_undesirable	Person Group Undesirable	Person Group Undesirable
risk-cards:lmrc:positive_reflections_death	Positive Reflections Death	Positive Reflections Death
risk-cards:lmrc:profanity	Profanity	Profanity
risk-cards:lmrc:prompt_extraction	Prompt Extraction	Prompt Extraction
risk-cards:lmrc:providing_illegal_info	Providing Illegal Info	Providing Illegal Info
risk-cards:lmrc:quack_medicine	Quack Medicine	Quack Medicine
risk-cards:lmrc:radicalisation	Radicalisation	Radicalisation
risk-cards:lmrc:reacting_to_self-harm	Reacting To Self-Harm	Reacting To Self-Harm
risk-cards:lmrc:sexual_content	Sexual Content	Sexual Content
risk-cards:lmrc:sexualisation	Sexualisation	Sexualisation
risk-cards:lmrc:slur_usage	Slur Usage	Slur Usage
risk-cards:lmrc:speculation	Speculation	Speculation
risk-cards:lmrc:stereotypes	Stereotypes	Stereotypes
risk-cards:lmrc:suicide_instructions	Suicide Instructions	Suicide Instructions
risk-cards:lmrc:support_hate	Support Hate	Support Hate
risk-cards:lmrc:supporting_medication_changes	Supporting Medication Changes	Supporting Medication Changes
risk-cards:lmrc:support_scam	Support Scam	Support Scam
risk-cards:lmrc:suppression_of_rights	Suppression Of Rights	Suppression Of Rights
risk-cards:lmrc:threats	Threats	Threats
risk-cards:lmrc:unproven_claims	Unproven Claims	Unproven Claims
risk-cards:lmrc:violation_of_privacy	Violation Of Privacy	Violation Of Privacy
risk-cards:lmrc:weapon_instructions	Weapon Instructions	Weapon Instructions
risk-cards:lmrc:wrong_tone	Wrong Tone	Wrong Tone
cwe:89	Improper Neutralization of Special Elements used in an SQL Command	The product constructs all or part of an SQL command using externally-influenced input from an upstream component, but it does not neutralize or incorrectly neutralizes special elements that could modify the intended SQL command when it is sent to a downstream component. Without sufficient removal or quoting of SQL syntax in user-controllable inputs, the generated SQL query can cause those inputs to be interpreted as SQL instead of ordinary user data.
cwe:94	Improper Control of Generation of Code ('Code Injection')	The product constructs all or part of a code segment using externally-influenced input from an upstream component, but it does not neutralize or incorrectly neutralizes special elements that could modify the syntax or behavior of the intended code segment.
cwe:95	Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')	The product receives input from an upstream component, but it does not neutralize or incorrectly neutralizes code syntax before using the input in a dynamic evaluation call (e.g. "eval").
cwe:1336	Improper Neutralization of Special Elements Used in a Template Engine	The product uses a template engine to insert or process externally-influenced input, but it does not neutralize or incorrectly neutralizes special elements or syntax that can be interpreted as template expressions or other code directives when processed by the engine.
cwe:1426	Improper Validation of Generative AI Output	The product invokes a generative AI/ML component whose behaviors and outputs cannot be directly controlled, but the product does not validate or insufficiently validates the outputs to ensure that they align with the intended security, content, or privacy policy.
cwe:1427	Improper Neutralization of Input Used for LLM Prompting	The product uses externally-provided data to build prompts provided to large language models (LLMs), but the way these prompts are constructed causes the LLM to fail to distinguish between user-supplied inputs and developer provided system directives.
cwe:352	Cross-Site Request Forgery (CSRF)	The web application does not, or cannot, sufficiently verify whether a request was intentionally provided by the user who sent the request, which could have originated from an unauthorized actor.  
