name: <PERSON><PERSON><PERSON> pip - install from repo

on:
  push:
    branches:
      - 'main'
  pull_request:
  workflow_dispatch:
  workflow_call:

permissions:
  actions: none
  checks: none
  contents: none
  deployments: none
  id-token: none
  issues: none
  discussions: none
  packages: none
  pages: none
  pull-requests: none
  repository-projects: none
  security-events: none
  statuses: none

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10","3.12","3.13"]
    steps:
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: pip install from repo
        run: |
          python -m pip install --upgrade pip
          python -m pip install -U git+https://github.com/${GITHUB_REPOSITORY}.git@${GITHUB_SHA}
      - name: Sanity Test
        run: |
          python -m garak --model_type test.Blank --probes test.Test
          set +e
          grep -E "(WARNING|ERROR|CRITICAL)" $HOME/.local/share/garak/garak.log
          if [ $? != 1 ]; then
            echo "Errors exist in the test log"
            exit 1
          fi