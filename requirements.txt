base2048>=0.1.3
transformers>=4.51.3
datasets>=3.0.0,<4.0
colorama>=0.4.3
tqdm>=4.67.1
cohere>=5.16.0
openai>=1.45.0,<2
replicate>=0.8.3
google-api-python-client>=2.0
backoff>=2.1.1
rapidfuzz>=3.0.0
jinja2>=3.1.6
nltk>=3.9.1
accelerate>=0.23.0
avidtools==0.1.2
stdlibs>=2022.10.9
langchain>=0.3.25
nemollm>=0.3.0
cmd2==2.4.3
torch>=2.6.0
sentencepiece>=0.1.99
markdown>=3.4.3
numpy>=2.0.0
zalgolib>=0.2.2
ecoji>=0.1.1
deepl==1.17.0
fschat>=0.2.36
litellm>=1.68.1
jsonpath-ng>=1.6.1
huggingface_hub>=0.21.0
python-magic-bin>=0.4.14; sys_platform == "win32"
python-magic>=0.4.21; sys_platform != "win32"
lorem==0.1.1
xdg-base-dirs>=6.0.1
wn==0.9.5
ollama>=0.4.7
nvidia-riva-client==2.16.0
google-cloud-translate>=2.0.4
grpcio-tools>=1.71.0
langdetect==1.0.9
tiktoken>=0.7.0
mistralai==1.5.2
pillow>=10.4.0
ftfy>=6.3.1
# tests
pytest>=8.0
pytest-mock>=3.14.0
requests-mock==1.12.1
respx>=0.21.1
pytest-cov>=5.0.0
pytest_httpserver>=1.1.0
langcodes>=3.4.0
# lint
black==24.4.2
pylint>=3.1.0
# calibration
scipy>=1.14.0
# audio
soundfile>=0.13.1
librosa>=0.10.2
