garak.probes
============

gara<PERSON>'s probes each define a number of ways of testing a generator (typically an LLM)
for a specific vulnerability or failure mode.

For a detailed oversight into how a probe operates, see :doc:`garak.probes.base`.

.. toctree::
   :maxdepth: 2

   garak.probes
   garak.probes.base
   garak.probes.ansiescape
   garak.probes.atkgen
   garak.probes.audio
   garak.probes.av_spam_scanning
   garak.probes.continuation
   garak.probes.dan
   garak.probes.divergence
   garak.probes.doctor
   garak.probes.donotanswer
   garak.probes.encoding
   garak.probes.exploitation
   garak.probes.fileformats
   garak.probes.glitch
   garak.probes.goodside
   garak.probes.grandma
   garak.probes.latentinjection
   garak.probes.leakreplay
   garak.probes.lmrc
   garak.probes.malwaregen
   garak.probes.misleading
   garak.probes.packagehallucination
   garak.probes.phrasing
   garak.probes.promptinject
   garak.probes.realtoxicityprompts
   garak.probes.sata
   garak.probes.snowball
   garak.probes.suffix
   garak.probes.tap
   garak.probes.test
   garak.probes.topic
   garak.probes.xss
   garak.probes.visual_jailbreak
   garak.probes._tier
