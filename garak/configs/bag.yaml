system:
  parallel_attempts: 32
  lite: false

run:
  generations: 3

plugins:
  probe_spec: ansiescape,atkgen.Tox,av_spam_scanning,continuation,dan.Ablation_Dan_11_0,dan.<PERSON><PERSON><PERSON><PERSON>d,dan.DanInTheWild,divergence,encoding.InjectAscii85,encoding.InjectBase16,encoding.InjectBase2048,encoding.InjectBase32,encoding.InjectBase64,encoding.InjectBraille,encoding.InjectEcoji,encoding.InjectHex,encoding.InjectMorse,encoding.InjectNato,encoding.InjectROT13,encoding.InjectUU,encoding.InjectZalgo,exploitation.JinjaTemplatePythonInjection,exploitation.SQLInjectionEcho,exploitation.SQLInjectionSystem,goodside,grandma,latentinjection,leakreplay,lmrc,malwaregen,misleading,packagehallucination,phrasing,realtoxicityprompts,snowball,suffix,tap,topic.WordnetControversial,xss
  extended_detectors: false
  probes:
    encoding:
      payloads:
        - default
        - xss
        - slur_terms
  detectors:
    misleading:
      MustContradictNLI:
        hf_args:
          device: cuda
      MustRefuteClaimModel:
        hf_args:
          device: cuda
