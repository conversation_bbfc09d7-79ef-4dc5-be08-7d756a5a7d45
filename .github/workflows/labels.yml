name: Labels

# https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#permissions
permissions:
  actions: none
  checks: none
  contents: none
  deployments: none
  id-token: none
  # This action can update/close issues
  issues: write
  discussions: none
  packages: none
  pages: none
  # This action can update/close pull requests
  pull-requests: write
  repository-projects: none
  security-events: none
  statuses: none

on:
  pull_request_target:
    types: [labeled]
  issues:
    types: [labeled]

jobs:
  handle-labels:
    if: github.repository_owner == 'NVIDIA'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v7
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            // NOTE: The following section is JavaScript. Note that backticks will need to be escaped within
            // the multiline comment strings in the following config. When editing this file, using JavaScript
            // syntax highlighting might be easier.
            //
            // This script has intentionally been inlined instead of using third-party Github actions for both
            // security and performance reasons.
            const allConfig = {
              pullRequests: {
                'needs linting': {
                  close: false,
                  comment: `
                    Thanks for your pull request! Before this pull request can be merged, it must pass the checks of our automated linting tools.

                    We use \`black\` to ensure the consistent format of our code. This can be run from the root directory of garak:

                    \`\`\`
                    black garak
                    \`\`\`

                    Please update your branch after these have been made, and reach out if you have any problems.
                  `
                },
                'needs unique branch': {
                  close: true,
                  comment: `
                    Thanks for your pull request! We require for all contributed code to come from a **from a unique branch** in your repository before it can be merged.

                    Please create a new branch in your fork of garak and resubmit this from that branch.

                    If you are using Git on the command line that may look like:

                    \`\`\`
                    # Checkout the main branch
                    git checkout main

                    # Create a new branch for your feature
                    git checkout -b <BRANCH_NAME>

                    # Add your new files
                    git add plugins/my-cool-new-plugin

                    # Commit your changes with a relevant message
                    git commit

                    # Push your changes to GitHub
                    git push origin <BRANCH_NAME>

                    # Now browse to the following URL and create your pull request!
                    # - https://github.com/NVIDIA/garak/pulls
                    \`\`\`

                    This helps protect the process, ensure users are aware of commits on the branch being considered for merge, allows for a location for more commits to be offered without mingling with other contributor changes and allows contributors to make progress while a PR is still being reviewed.

                    Please do resubmit from a unique branch, we greatly value your contribution! :tada:
                  `
                },
                'needs testing environment': {
                  close: false,
                  comment: `
                    Thanks for your pull request! As part of our landing process, we manually verify that all plugins work as expected.

                    We have been unable to test this plugin successfully. This may be due to software or hardware requirements we cannot replicate.

                    To help unblock this pull request, please:

                    - Comment with links to documentation on how to set up an environment, and provide exact software version numbers to use
                    - Or comment guided steps on how to set up our environment for testing this plugin

                    Once there's a clear path for testing and evaluating this code, we can progress with this further.
                  `
                },
                'needs pull request template': {
                  close: false,
                  comment: `
                    When creating a pull request, please ensure that the default pull request template has been updated with the required details.
                  `
                },
              },
              issues: {
                'needs more information': {
                  close: false,
                  comment: `
                    It looks like there's not enough information to replicate this issue. Please provide any relevant output and logs which may be useful in diagnosing the issue.

                    This includes:

                    - All of the item points within this [template](https://github.com/NVIDIA/garak/blob/master/.github/ISSUE_TEMPLATE/bug_report.md)
                    - Screenshots showing the issues you're having
                    - Exact replication steps

                    The easier it is for us to replicate and debug an issue means there's a higher chance of this issue being resolved.
                  `
                },
                // Used for issues that have zero effort applied, potentially bot related
                potato: {
                  close: true,
                  comment: `
                    When creating an issue, please ensure that the default issue template has been updated with the required details:
                    https://github.com/NVIDIA/garak/issues/new/choose

                    Closing this issue. If you believe this issue has been closed in error, please provide any relevant output and logs which may be useful in diagnosing the issue.
                  `
                },
              }
            };

            const issueType = context.eventName === 'issues' ? 'issues' : 'pullRequests';
            const config = allConfig[issueType][context.payload.label.name];
            if (!config) {
              return;
            }

            if (config.comment) {
              const precedingWhitespaceLength = config.comment.split("\n")[1].search(/\S/);
              const commentWithoutPrecedingWhitespace = config.comment.split("\n").map(line => line.substring(precedingWhitespaceLength)).join("\n").trim();
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: commentWithoutPrecedingWhitespace
              });
            }
            if (config.close) {
              await github.rest.issues.update({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  state: 'closed'
              });
            }
