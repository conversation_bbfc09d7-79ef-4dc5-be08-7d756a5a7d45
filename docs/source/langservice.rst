garak.langservice
=================

The ``langservice`` module in garak is designed to handle text language tasks using various translation services and models.
It provides an entry point to translation support backed by translators, each implementing different translation strategies and models, including both cloud-based services,
like `DeepL <https://www.deepl.com/>`_ and `NVIDIA Riva <https://build.nvidia.com/nvidia/megatron-1b-nmt>`_, and local models like facebook/m2m100 available on `Hugging Face <https://huggingface.co/>`_.

.. automodule:: garak.langservice
   :members:
   :undoc-members:
   :show-inheritance:
