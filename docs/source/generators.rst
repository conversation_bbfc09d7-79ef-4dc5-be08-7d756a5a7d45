garak.generators
================

garak's generators each wrap a set of ways for interfacing with a dialogue system or LLM.

For a detailed oversight into how a generator operates, see :doc:`garak.generators.base`.

.. toctree::
   :maxdepth: 2

   garak.generators
   garak.generators.azure
   garak.generators.base
   garak.generators.cohere
   garak.generators.function
   garak.generators.ggml
   garak.generators.groq
   garak.generators.guardrails
   garak.generators.huggingface
   garak.generators.langchain
   garak.generators.langchain_serve
   garak.generators.litellm
   garak.generators.mistral
   garak.generators.ollama
   garak.generators.openai
   garak.generators.nemo
   garak.generators.nim
   garak.generators.nvcf
   garak.generators.replicate
   garak.generators.rest
   garak.generators.rasa
   garak.generators.test
   garak.generators.watsonx
