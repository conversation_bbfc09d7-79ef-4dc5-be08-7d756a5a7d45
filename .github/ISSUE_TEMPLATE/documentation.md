---
name: Documentation 📝
about: Suggest better docs coverage for a particular tool or process.
labels: "documentation"
---

<!--
  To make it easier for us to help you, please include as much useful information as possible.

  Useful Links:
  - Wiki: https://docs.garak.ai/garak
  - Code reference: https://reference.garak.ai/

  Before opening a new issue, please search existing issues https://github.com/NVIDIA/garak/issues
-->

## Summary

What problem(s) did you run into that caused you to request additional documentation? What questions do you think we should answer? What, if any, existing documentation relates to this proposal?

Some recommended topics to cover:

- List the topics you think should be here.
- This list does not need to be exhaustive!

### Motivation

Why should we document this and who will benefit from it?

## Steps to resolve this issue

<!-- Your suggestion may require additional steps. Remember to add any relevant labels. Note that you'll need to fill in the link to a similar article as well as the correct section. Don't worry if you're not yet sure about these, especially if this is a brand new topic! -->

### Draft the doc

- [ ] If you can, write the doc, following the format listed in these resources:
  - [Example of a similar article]()

### Open a pull request

- [ ] Open a pull request with your work including the words "closes #[this issue's number]" in the pull request description