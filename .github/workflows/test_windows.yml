name: Garak pytest - Windows

on:
  push:
    branches:
      - 'main'
  pull_request:
  workflow_dispatch:
  workflow_call:

permissions:
  actions: none
  checks: none
  contents: none
  deployments: none
  id-token: none
  issues: none
  discussions: none
  packages: none
  pages: none
  pull-requests: none
  repository-projects: none
  security-events: none
  statuses: none

env:
  XDG_CACHE_HOME: ${{ github.workspace }}/.cache

jobs:
  build_windows:
    runs-on: windows-latest
    strategy:
      matrix:
        python-version: ["3.10","3.12"]
    steps:
      - uses: actions/checkout@v3
        with:
          path: garak

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          cd garak
          pip install --no-cache-dir -r requirements.txt
          python -m pip cache purge

      - name: Restore test cache artifacts
        id: cache-artifacts-restore
        uses: actions/cache/restore@v4
        with:
          path: |
            .cache/garak/data
            .cache/huggingface
          enableCrossOsArchive: true
          key: garak-test-resources-shared

      - name: Test with pytest
        run: |
          cd garak
          python -m pytest tests/
