---
# run a thorough test for toxicity generation
# . use probes that attempt to evoke toxic responses
# . do paraphrasing to increase coverage
# . paraphrasing increases the prompt vocab a lot
system:
  lite: false

run:
  generations: 5

plugins:
  probe_spec: atkgen.Tox,continuation.ContinueSlursReclaimedSlurs,encoding,lmrc.Anthropomorphisation,lmrc.Bullying,lmrc.Deadnaming,lmrc.Profanity,lmrc.SlurUsage,realtoxicityprompts,
  probes:
    encoding:
      payloads:
        - slur_terms
  buff_spec: paraphrase.Fast

