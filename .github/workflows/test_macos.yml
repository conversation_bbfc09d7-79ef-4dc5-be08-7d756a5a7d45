name: <PERSON><PERSON><PERSON> pytest - <PERSON><PERSON>

on:
  push:
    branches:
      - 'main'
  pull_request:
  workflow_dispatch:
  workflow_call:
    inputs:
      store-cache:
        description: "Store resource cache"
        required: false
        type: boolean


permissions:
  actions: none
  checks: none
  contents: none
  deployments: none
  id-token: none
  issues: none
  discussions: none
  packages: none
  pages: none
  pull-requests: none
  repository-projects: none
  security-events: none
  statuses: none

env:
  XDG_CACHE_HOME: ${{ github.workspace }}/.cache

jobs:
  build_macos:
    runs-on: macos-latest
    strategy:
      matrix:
        python-version: ["3.10","3.12","3.13"]
    steps:
      - uses: actions/checkout@v3
        with:
          path: garak

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          brew install libmagic
          cd garak
          python -m pip install --upgrade pip
          pip install --no-cache-dir -r requirements.txt
          python -m pip cache purge

      - name: Restore test cache artifacts
        id: cache-artifacts-restore
        uses: actions/cache/restore@v4
        with:
          path: |
            .cache/garak/data
            .cache/huggingface
          key: garak-test-resources-shared

      - name: More cached models
        run: |
          export HF_HUB_DISABLE_XET=1
          # download translation support models too large for standard cache
          huggingface-cli download facebook/m2m100_418M --quiet
          huggingface-cli download Helsinki-NLP/opus-mt-en-fr --quiet
          huggingface-cli download Helsinki-NLP/opus-mt-fr-en --quiet
          huggingface-cli download Helsinki-NLP/opus-mt-en-jap --quiet
          huggingface-cli download Helsinki-NLP/opus-mt-jap-en --quiet

      - name: Test with pytest
        run: |
          cd garak
          python -m pytest tests/

      - name: Prepare resources for cache
        run: |
          rm -rf .cache/huggingface/hub/*facebook*
          rm -rf .cache/huggingface/hub/*Helsinki*
          rm -rf .cache/huggingface/hub/*roberta*

      - name: Save test cache
        if: inputs.store-cache
        uses: actions/cache@v4
        with:
          path: |
            .cache/garak/data
            .cache/huggingface
          enableCrossOsArchive: true
          key: garak-test-resources-shared
