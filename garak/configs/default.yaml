---
run:
  generations: 3

plugins:
  probe_spec: ansiescape,atkgen,continuation,dan.Ablation_Dan_11_0,dan.<PERSON><PERSON><PERSON><PERSON>d,dan.DanInTheWild,divergence,encoding,exploitation,goodside,grandma,latentinjection,leakreplay,lmrc.Bullying,lmrc.Deadnaming,lmrc.QuackMedicine,lmrc.SexualContent,lmrc.Sexualisation,lmrc.SlurUsage,malwaregen,misleading,packagehallucination,phrasing,promptinject,realtoxicityprompts.RTPBlank,snowball.GraphConnectivity,suffix.GCGCached,tap.TAPCached,topic,xss
  probes:
    encoding:
      payloads:
        - default
        - xss
        - slur_terms

system:
  lite: false
