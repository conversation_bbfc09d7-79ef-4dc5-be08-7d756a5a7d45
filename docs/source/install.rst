..  headings: = - ^ "
Installation
============

.. _installation:

garak is a command-line tool.
It is developed in Linux, MacOS, and Windows.

Installing with pip
---------------------

.. code-block:: console

   $ python3 -m pip install garak


Installing the Development Version
----------------------------------

The version of garak is updated periodically.
To install from source in GitHub, run the following command:

.. code-block:: console

   $ python3 -m pip install -U git+https://github.com/NVIDIA/garak.git@main
