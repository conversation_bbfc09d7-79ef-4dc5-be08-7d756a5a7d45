array
bool
char
f32
f64
fn
i8
i16
i32
i64
i128
isize
pointer
reference
slice
str
tuple
u8
u16
u32
u64
u128
unit
usize
f16Experimental
f128Experimental
neverExperimental
Modules
alloc
any
arch
array
ascii
backtrace
borrow
boxed
cell
char
clone
cmp
collections
convert
default
env
error
f32
f64
ffi
fmt
fs
future
hash
hint
i8Deprecation
i16Deprecation
i32Deprecation
i64Deprecation
i128Deprecation
io
isizeDeprecation
iter
marker
mem
net
num
ops
option
os
panic
path
pin
prelude
primitive
process
ptr
rc
result
slice
str
string
sync
task
thread
time
u8Deprecation
u16Deprecation
u32Deprecation
u64Deprecation
u128Deprecation
usizeDeprecation
vec
assert_matchesExperimental
async_iterExperimental
autodiffExperimental
f16Experimental
f128Experimental
intrinsicsExperimental
patExperimental
pipeExperimental
randomExperimental
simdExperimental
Macros
assert
assert_eq
assert_ne
cfg
column
compile_error
concat
dbg
debug_assert
debug_assert_eq
debug_assert_ne
env
eprint
eprintln
file
format
format_args
include
include_bytes
include_str
is_x86_feature_detected
line
matches
module_path
option_env
panic
print
println
stringify
thread_local
todo
tryDeprecated
unimplemented
unreachable
vec
write
writeln
cfg_matchExperimental
concat_bytesExperimental
concat_identsExperimental
const_format_argsExperimental
format_args_nlExperimental
log_syntaxExperimental
trace_macrosExperimental
Keywords
SelfTy
as
async
await
break
const
continue
crate
dyn
else
enum
extern
false
fn
for
if
impl
in
let
loop
match
mod
move
mut
pub
ref
return
self
static
struct
super
trait
true
type
union
unsafe
use
where
while
