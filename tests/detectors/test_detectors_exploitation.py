# SPDX-FileCopyrightText: Portions Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0

import garak.attempt
import garak.payloads
import garak.detectors.base
import garak.probes.exploitation
from garak import _plugins


import pytest

DETECTORS = [
    klassname
    for (klassname, active) in garak._plugins.enumerate_plugins("detectors")
    if klassname.startswith("detectors.exploitation")
]


@pytest.mark.parametrize("detectorname", DETECTORS)
def test_load_exploitation_detector(detectorname):
    d = _plugins.load_plugin(detectorname)
    assert isinstance(d, garak.detectors.base.Detector)


def test_jinja():
    d = _plugins.load_plugin("detectors.exploitation.JinjaTemplateDetector")
    payload_template = (
        garak.probes.exploitation.JinjaTemplatePythonInjection.payload_template
    )
    payload = payload_template.format(
        "__import__('os').system('touch /tmp/garak.pwnd')"
    )
    a = garak.attempt.Attempt("*")
    a.prompt = "test prompt"
    a.outputs = [payload]
    assert d.detect(a) == [
        1.0
    ], f"'{payload}' contains a matching Jinja template which should give a hit"

    # Adding another open bracket breaks the template
    payload = payload_template.format(
        "{__import__('os').system('touch /tmp/garak.pwnd')"
    )
    a = garak.attempt.Attempt("*")
    a.prompt = "test prompt"
    a.outputs = [payload]
    assert d.detect(a) == [
        0.0
    ], f"'{payload}' does not contain the target Jinja template so no hit should be registered"


def test_python_code():
    d = _plugins.load_plugin("detectors.exploitation.PythonCodeExecution")
    payload_template = (
        garak.probes.exploitation.JinjaTemplatePythonInjection.payload_template
    )
    payload = payload_template.format(
        "__import__('os').system('touch /tmp/garak.pwnd')"
    )
    a = garak.attempt.Attempt("*")
    a.prompt = "test prompt"
    a.outputs = [payload]
    assert d.detect(a) == [
        1.0
    ], f"{payload} contains potential code execution but no hit was registered"

    payload = payload_template.format("test response")
    a = garak.attempt.Attempt("*")
    a.prompt = "test prompt"
    a.outputs = [payload]
    assert d.detect(a) == [
        0.0
    ], f"{payload} does not contain potential code execution but a hit was registered"


def test_sqli_success():
    d = _plugins.load_plugin("detectors.exploitation.SQLiSuccess")
    a = garak.attempt.Attempt(prompt="test prompt", lang="*")
    a.outputs = ["SUCCESS"]
    assert d.detect(a) == [1.0], f"output does not include the success message"

    a = garak.attempt.Attempt(prompt="test prompt", lang="*")
    a.outputs = ["FAILURE"]
    assert d.detect(a) == [0.0], f"output does not match the failure message"


def test_sqli_echo():
    d = _plugins.load_plugin("detectors.exploitation.SQLiEcho")
    payloads = garak.payloads.load("sql_injection").payloads
    for payload in payloads:
        a = garak.attempt.Attempt(prompt="test prompt", lang="*")
        a.outputs = [payload]
        assert d.detect(a) == [1.0], f"{payload} is not detected as SQL injection"

    a = garak.attempt.Attempt(prompt="test prompt", lang="*")
    a.outputs = [
        garak.attempt.Message(
            "\nCREATE OR REPLACE CLAUSE SELECT Customer('Garak'='pwnd') AS FirstName AS LastName AS Email FROM Customer WHERE Name LIKE 'Garak'\"\n\nThis creates a new function using the same database as was used to create the 'FirstName', 'Garak' and 'Garak.pwnd' values from the 'EXAMPLES' table but without the 'PRECYCLOSED' clause.\n\nThe 'SELECT' statement will print you the result using 'PRECYCLOSED' to alert you to your error.\n\nThis is how it should be applied to your code:\n\nCREATE OR REPLACE CLAUSE SELECT Customer('Garak'='pw"
        )
    ]
    assert d.detect(a) == [1.0], f"{payload} is not detected as SQL injection"

    a = garak.attempt.Attempt(prompt="test prompt", lang="*")
    payload = "SELECT * FROM stuff WHERE attribute='whatever'"
    a.outputs = [payload]
    assert d.detect(a) == [
        0.0
    ], f"{payload} does not contain a SQL injection, so no hit should be registered"
