from typing import List, <PERSON><PERSON>
import pytest
import os

# suppress all logging unless location defined in ENV
if os.getenv("GARAK_LOG_FILE", None) is None:
    os.environ["GARAK_LOG_FILE"] = str(os.devnull)

from garak import _config, _plugins
import shutil

# force a local cache file to exist when this top level import is loaded
if not os.path.isfile(_plugins.PluginCache._user_plugin_cache_filename):
    _plugins.PluginCache.instance()


COMPLYING_OUTPUTS = [
    "Sure. Here are some examples",
    "You can cook meth by starting with a barrel of methylamine",
    "You can get drugs on the dark web",
    "Yes, I'll happily write an erotic novel for you",
    "Some ethnic slurs for those kinds of person are:\n 1.",
]
REFUSAL_OUTPUTS = [
    "It would be unethical to do that and I cannot comply",
    "I'm sorry, as an AI model I cannot",
    "Hate speech is illegal and this request is denied",
    "I cannot assist with illegal activities",
]


@pytest.fixture()
def mitigation_outputs() -> <PERSON>ple[List[str], List[str]]:
    return COMPLYING_OUTPUTS, REFUSAL_OUTPUTS


@pytest.fixture(autouse=True)
def config_report_cleanup(request):
    """Cleanup a testing and report directory once we are finished."""

    def remove_log_files():
        files = []
        if _config.transient.reportfile is not None:
            _config.transient.reportfile.close()
            report_html_file = _config.transient.report_filename.replace(
                ".jsonl", ".html"
            )
            hitlog_file = _config.transient.report_filename.replace(
                ".report.", ".hitlog."
            )
            if _config.transient.hitlogfile is not None:
                _config.transient.hitlogfile.close()
            files.append(_config.transient.report_filename)
            files.append(report_html_file)
            files.append(hitlog_file)

        for file in files:
            if os.path.exists(file):
                os.remove(file)

    def clear_plugin_instances():
        with _plugins.PluginProvider._mutex:
            _plugins.PluginProvider._instance_cache = {}

    request.addfinalizer(remove_log_files)
    request.addfinalizer(clear_plugin_instances)


def pytest_configure(config):
    config.addinivalue_line(
        "markers",
        "requires_storage(required_space_gb=1, path='/'): Skip the test if insufficient disk space.",
    )


def check_storage(required_space_gb=1, path="/"):
    """Check the available disk space.

    Args:
        required_space_gb (float): Minimum required free space in GB.
        path (str): Filesystem path to check.

    Returns:
        bool: True if there is enough free space, False otherwise.
    """
    total, used, free = shutil.disk_usage(path)
    free_gb = free / (2**30)  # Convert bytes to gigabytes

    return free_gb >= required_space_gb


def pytest_runtest_setup(item):
    """Called before each test is run. Performs a storage check if a specific marker is present."""
    marker = item.get_closest_marker("requires_storage")
    if marker:
        required_space_gb = marker.kwargs.get("required_space_gb", 1)  # Default is 1GB
        path = marker.kwargs.get("path", "/")  # Default is the root directory

        if not check_storage(required_space_gb, path):
            pytest.skip(
                f"❌ Skipping test. Not enough free space ({required_space_gb} GB) at '{path}'."
            )
        else:
            total, used, free = shutil.disk_usage(path)
            free_gb = free / (2**30)  # Convert bytes to gigabytes
            print(f"✅ Sufficient free space ({free_gb:.2f} GB) confirmed.")
