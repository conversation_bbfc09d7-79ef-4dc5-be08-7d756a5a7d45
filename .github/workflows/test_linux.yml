name: <PERSON>ara<PERSON> pytest - Linux

on:
  push:
    branches:
      - 'main'
  pull_request:
  workflow_dispatch:
  workflow_call:

permissions:
  actions: none
  checks: none
  contents: none
  deployments: none
  id-token: none
  issues: none
  discussions: none
  packages: none
  pages: none
  pull-requests: none
  repository-projects: none
  security-events: none
  statuses: none

env:
  XDG_CACHE_HOME: ${{ github.workspace }}/.cache

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, ubuntu-24.04-arm]
        python-version: ["3.10","3.12","3.13"]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Reduce disk usage
        if: ${{ matrix.os }} == 'ubuntu-latest'
        run: |
          sudo rm -rf /usr/local/lib/android || true
          sudo rm -rf /usr/share/dotnet || true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install --no-cache-dir -r requirements.txt
          python -m pip cache purge

      - name: Restore test cache artifacts
        id: cache-artifacts-restore
        uses: actions/cache/restore@v4
        with:
          path: |
            .cache/garak/data
            .cache/huggingface
          key: garak-test-resources-shared

      - name: Test with pytest
        run: |
          python -m pytest tests/
