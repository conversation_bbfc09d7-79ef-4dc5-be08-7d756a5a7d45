[build-system]
requires = ["flit_core >=3.11,<4"]
build-backend = "flit_core.buildapi"

[project]
name = "garak"
version = "0.13.1.pre1"
authors = [
  { name = "<PERSON>", email="<PERSON><PERSON><PERSON><PERSON><PERSON>@nvidia.com" },
  { name = "<PERSON><PERSON>", email="<EMAIL>" },
  { name = "nv052193" },
  { name = "Mad<PERSON>k" },
  { name = "<PERSON><PERSON>", email="<EMAIL>" },
  { name = "<PERSON><PERSON><PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON><PERSON><PERSON>" },
  { name = "<PERSON><PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON>", email="<EMAIL>" },
  { name = "<PERSON><PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON><PERSON>" },
  { name = "<PERSON><PERSON><PERSON>" },
  { name = "<PERSON><PERSON><PERSON>" },
  { name = "<PERSON><PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON><PERSON>" },
  { name = "<PERSON> <PERSON>" },
  { name = "<PERSON><PERSON><PERSON>" },
  { name = "<PERSON>-af<PERSON>" },
  { name = "<PERSON> <PERSON><PERSON><PERSON>" },  
  { name = "<PERSON> <PERSON><PERSON>" },
  { name = "<PERSON> <PERSON>yo" },
  { name = "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>" },
  { name = "i<PERSON>not<PERSON><PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON> <PERSON><PERSON>" },
  { name = "Masaya Ogushi" },
  { name = "Viktor T. Zetterberg" },
  { name = "Erwan Roussel" },
  { name = "Matthew Rowe" },
  { name = "Aishwarya Padmakumar" },
  { name = "Marco Rosa" },
  { name = "Ian Chu" },
  { name = "Mike McKiernan" },
  { name = "Divya Chitimalla" },
  { name = "Katherine Luna" },
  { name = "Dave Baker" },
  { name = "Jack Kelly" },
  { name = "Amrit Prakash" },
]
license = "Apache-2.0"
license-files = ["LICENSE"]
description = "LLM vulnerability scanner"
readme = "README.md"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
requires-python = ">=3.10"
dependencies = [
  "base2048>=0.1.3",
  "transformers>=4.51.3",
  "datasets>=3.0.0,<4.0",
  "colorama>=0.4.3",
  "tqdm>=4.67.1",
  "cohere>=5.16.0",
  "openai>=1.45.0,<2",
  "replicate>=0.8.3",
  "google-api-python-client>=2.0",
  "backoff>=2.1.1",
  "rapidfuzz>=3.0.0",
  "jinja2>=3.1.6",
  "nltk>=3.9.1",
  "accelerate>=0.23.0",
  "avidtools==0.1.2",
  "stdlibs>=2022.10.9",
  "langchain>=0.3.25",
  "nemollm>=0.3.0",
  "cmd2==2.4.3",
  "torch>=2.6.0",
  "sentencepiece>=0.1.99",
  "markdown>=3.4.3",
  "numpy>=2.0.0",
  "zalgolib>=0.2.2",
  "ecoji>=0.1.1",
  "deepl==1.17.0",
  "fschat>=0.2.36",
  "litellm>=1.68.1",
  "jsonpath-ng>=1.6.1",
  "huggingface_hub>=0.21.0",
  'python-magic-bin>=0.4.14; sys_platform == "win32"',
  'python-magic>=0.4.21; sys_platform != "win32"',
  "lorem==0.1.1",
  "xdg-base-dirs>=6.0.1",
  "wn==0.9.5",
  "ollama>=0.4.7",
  "nvidia-riva-client==2.16.0",
  "google-cloud-translate>=2.0.4",
  "grpcio-tools>=1.71.0",
  "langdetect==1.0.9",
  "tiktoken>=0.7.0",
  "mistralai==1.5.2",
  "pillow>=10.4.0",
  "ftfy>=6.3.1",
]

[project.optional-dependencies]
tests = [
  "pytest>=8.0",
  "pytest-mock>=3.14.0",
  "requests-mock==1.12.1",
  "respx>=0.21.1",
  "pytest-cov>=5.0.0",
  "pytest_httpserver>=1.1.0",
  "langcodes>=3.4.0",
]
lint = [
  "black==24.4.2",
  "pylint>=3.1.0",
]
calibration = [
  "scipy>=1.14.0",
]
audio = [
  "soundfile>=0.13.1",
  "librosa>=0.10.2"
]

[project.urls]
"Homepage" = "https://github.com/NVIDIA/garak"
"Bug Tracker" = "https://github.com/NVIDIA/garak/issues"

[project.scripts]
garak = "garak.__main__:main"

[tool.flit.module]
name = "garak"

[tool.flit.sdist]
include = [
  "*.md",
  "*.txt",
  "garak/analyze/templates/*",
  "garak/configs/*",
  "garak/resources/*.json",
  "garak/resources/*.yaml",
]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'

[tool.pytest.ini_options]
filterwarnings = [
    "ignore",
    "default:::garak",
]

[tool.coverage.run]
source = ["./garak"]
omit = ["tests/*"]
